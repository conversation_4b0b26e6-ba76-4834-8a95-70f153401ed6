// ==UserScript==
// @name         YouTube Audio HD - Micro
// @namespace    https://tampermonkey.com/
// @version      6.1
// @description  Ultra-minimal HD audio enhancement
// @icon         https://www.google.com/s2/favicons?sz=64&domain=youtube.com
// <AUTHOR>
// @match        https://www.youtube.com/*
// @match        https://music.youtube.com/*
// @match        https://m.youtube.com/*
// @license      MIT
// @grant        GM_getValue
// @grant        GM_setValue
// ==/UserScript==

(function() {
  'use strict';

  // Minimal audio processor
  class MicroAudioProcessor {
    constructor() {
      this.ctx = null;
      this.nodes = {};
      this.settings = {
        gain: GM_getValue('gain', 1.2),
        bass: GM_getValue('bass', 1.8),
        treble: GM_getValue('treble', 1.3),
        presence: GM_getValue('presence', 1.25)
      };
    }

    async init() {
      try {
        const video = await this.findVideo();
        if (!video) return;

        // Create audio context and nodes
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        this.ctx = new AudioContext();
        if (this.ctx.state === 'suspended') await this.ctx.resume();

        // Create processing chain
        const source = this.ctx.createMediaElementSource(video);
        this.nodes = {
          gain: this.ctx.createGain(),
          bass: this.createFilter('peaking', 80, 1.5),
          treble: this.createFilter('highshelf', 8000, 0.7),
          presence: this.createFilter('peaking', 2800, 1.2)
        };

        // Connect chain
        source
          .connect(this.nodes.gain)
          .connect(this.nodes.bass)
          .connect(this.nodes.treble)
          .connect(this.nodes.presence)
          .connect(this.ctx.destination);

        this.updateSettings();
        this.createIndicator();
        console.log('🎵 Audio enhanced');
      } catch (e) {
        console.error('Audio init failed:', e);
      }
    }

    createFilter(type, freq, q) {
      const filter = this.ctx.createBiquadFilter();
      filter.type = type;
      filter.frequency.value = freq;
      filter.Q.value = q;
      return filter;
    }

    updateSettings() {
      if (!this.nodes.gain) return;
      this.nodes.gain.gain.value = this.settings.gain;
      this.nodes.bass.gain.value = (this.settings.bass - 1) * 12;
      this.nodes.treble.gain.value = (this.settings.treble - 1) * 8;
      this.nodes.presence.gain.value = (this.settings.presence - 1) * 6;
    }

    saveSettings() {
      Object.entries(this.settings).forEach(([k, v]) => GM_setValue(k, v));
    }

    async findVideo() {
      for (let i = 0; i < 10; i++) {
        const video = document.querySelector('video.html5-main-video') || document.querySelector('video');
        if (video) return video;
        await new Promise(r => setTimeout(r, 200));
      }
      return null;
    }

    createIndicator() {
      const div = document.createElement('div');
      div.innerHTML = '🎵';
      div.style.cssText = `
        position: fixed; bottom: 20px; right: 20px; padding: 8px;
        background: #667eea; color: white; border-radius: 50%;
        font-size: 16px; cursor: pointer; z-index: 9999; opacity: 0.8;
      `;
      div.onclick = () => this.showControls();
      document.body.appendChild(div);
      setTimeout(() => div.style.opacity = '0.5', 5000);
    }

    showControls() {
      const dialog = document.createElement('div');
      dialog.style.cssText = `
        position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
        background: #2d2d2d; color: white; padding: 20px; border-radius: 8px;
        z-index: 10000; min-width: 300px;
      `;

      dialog.innerHTML = `
        <h3>🎛️ Audio Controls</h3>
        ${this.createSlider('gain', 'Volume', 0.5, 2.5)}
        ${this.createSlider('bass', 'Bass', 0.5, 3)}
        ${this.createSlider('treble', 'Treble', 0.5, 3)}
        ${this.createSlider('presence', 'Presence', 0.5, 3)}
        <div style="margin-top: 15px;">
          <button id="preset-bass" style="margin: 2px; padding: 8px 12px; background: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer;">🔊 Bass</button>
          <button id="preset-clear" style="margin: 2px; padding: 8px 12px; background: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer;">✨ Clear</button>
          <button id="preset-balanced" style="margin: 2px; padding: 8px 12px; background: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer;">⚖️ Balanced</button>
        </div>
        <button id="close-dialog" style="float: right; margin-top: 10px; padding: 8px 12px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">Close</button>
      `;

      // Store dialog reference for updates
      this.currentDialog = dialog;

      // Setup sliders
      dialog.querySelectorAll('input').forEach(slider => {
        slider.oninput = (e) => {
          const val = parseFloat(e.target.value);
          this.settings[slider.id] = val;
          dialog.querySelector(`#${slider.id}-val`).textContent = val.toFixed(1);
          this.updateSettings();
          this.saveSettings();
        };
      });

      // Setup preset buttons
      dialog.querySelector('#preset-bass').onclick = () => this.applyPreset('bass');
      dialog.querySelector('#preset-clear').onclick = () => this.applyPreset('clear');
      dialog.querySelector('#preset-balanced').onclick = () => this.applyPreset('balanced');

      // Setup close button
      dialog.querySelector('#close-dialog').onclick = () => {
        this.currentDialog = null;
        dialog.remove();
      };

      document.body.appendChild(dialog);

      // Auto-close with cleanup
      setTimeout(() => {
        if (dialog.parentNode) {
          this.currentDialog = null;
          dialog.remove();
        }
      }, 30000);
    }

    createSlider(id, label, min, max) {
      const val = this.settings[id];
      return `
        <div style="margin: 10px 0; display: flex; justify-content: space-between; align-items: center;">
          <label>${label}:</label>
          <input type="range" id="${id}" min="${min}" max="${max}" step="0.1" value="${val}" style="width: 120px;">
          <span id="${id}-val" style="width: 30px;">${val.toFixed(1)}</span>
        </div>
      `;
    }

    applyPreset(type) {
      const presets = {
        bass: { gain: 1.3, bass: 2.5, treble: 1.1, presence: 1.0 },
        clear: { gain: 1.2, bass: 1.2, treble: 1.8, presence: 1.6 },
        balanced: { gain: 1.15, bass: 1.5, treble: 1.3, presence: 1.25 }
      };

      if (presets[type]) {
        // Visual feedback for button click
        if (this.currentDialog) {
          const button = this.currentDialog.querySelector(`#preset-${type}`);
          if (button) {
            button.style.background = '#4caf50';
            setTimeout(() => {
              button.style.background = '#667eea';
            }, 200);
          }
        }

        Object.assign(this.settings, presets[type]);
        this.updateSettings();
        this.saveSettings();
        this.updateDialogSliders();
        this.notify(`${type.charAt(0).toUpperCase() + type.slice(1)} preset applied`);
      }
    }

    preset(type) {
      // Keep this method for backward compatibility and menu commands
      this.applyPreset(type);
    }

    updateDialogSliders() {
      if (!this.currentDialog) return;

      // Update all sliders and their value displays
      Object.entries(this.settings).forEach(([key, value]) => {
        const slider = this.currentDialog.querySelector(`#${key}`);
        const valueDisplay = this.currentDialog.querySelector(`#${key}-val`);

        if (slider && valueDisplay) {
          slider.value = value;
          valueDisplay.textContent = value.toFixed(1);
        }
      });
    }

    notify(msg) {
      const div = document.createElement('div');
      div.textContent = msg;
      div.style.cssText = `
        position: fixed; top: 20px; right: 20px; padding: 10px;
        background: #4caf50; color: white; border-radius: 4px; z-index: 10001;
      `;
      document.body.appendChild(div);
      setTimeout(() => div.remove(), 2000);
    }
  }

  // Initialize
  const audioProcessor = new MicroAudioProcessor();
  window.audioProcessor = audioProcessor;

  // Auto-start
  const start = () => setTimeout(() => audioProcessor.init(), 1000);
  
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', start);
  } else {
    start();
  }

  // Handle page navigation
  let url = location.href;
  new MutationObserver(() => {
    if (location.href !== url) {
      url = location.href;
      setTimeout(() => audioProcessor.init(), 1500);
    }
  }).observe(document.body, { childList: true, subtree: true });

})();
